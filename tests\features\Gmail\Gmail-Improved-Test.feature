Feature: Gmail Service - Improved Latest Email Detection

  Background:
    Given the Gmail service is configured and authenticated

  @GmailImprovedLatest
  Scenario Outline: Test improved latest email detection with buffer time
    When I wait for the latest email with subject containing "<SubjectText>" with <BufferSeconds> second buffer
    Then I should receive the most recent email from that sender
    And the email should have a valid date and content

    @GmailImprovedLatest_Test
    Examples:
      | SubjectText           | BufferSeconds |
      | Sensa Forgot Password | 10            |

  @GmailTimestampControl
  Scenario Outline: Test precise timestamp control for email detection
    When I record the current timestamp and wait for email with subject containing "<SubjectText>"
    Then I should receive the most recent email from that sender
    And the email should have a valid date and content

    @GmailTimestampControl_Test
    Examples:
      | SubjectText           |
      | Sensa Forgot Password |

  @GmailBasicImproved
  Scenario Outline: Test basic improved email detection
    When I get the latest email with subject containing "<SubjectText>"
    Then I should receive the most recent email from that sender
    And the email should have a valid date and content

    @GmailBasicImproved_Test
    Examples:
      | SubjectText           |
      | Sensa Forgot Password |
