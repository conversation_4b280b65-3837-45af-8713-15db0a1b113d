import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import GmailUtil, { EmailObject } from './gmail.util';

// Mock the browser global
const mockBrowser = {
    checkInbox: jest.fn(),
};

// Mock the global browser object
(global as any).browser = mockBrowser;

// Mock logger
jest.mock('./logger.util.ts', () => ({
    info: jest.fn(),
    error: jest.fn(),
}));

describe('GmailUtil - waitForLatestEmailBySubject', () => {
    let gmailUtil: GmailUtil;
    
    beforeEach(() => {
        gmailUtil = new GmailUtil();
        jest.clearAllMocks();
    });

    const createMockEmail = (date: string, from: string, subject: string): EmailObject => ({
        from,
        subject,
        date,
        body: {
            html: '<p>Test email content</p>',
            text: 'Test email content'
        }
    });

    it('should return the latest email when multiple emails exist', async () => {
        const oldEmail = createMockEmail('2024-01-01T10:00:00Z', '<EMAIL>', 'Test Subject');
        const newEmail = createMockEmail('2024-01-01T11:00:00Z', '<EMAIL>', 'Test Subject');
        
        // First call returns old email, second call returns both emails
        mockBrowser.checkInbox
            .mockResolvedValueOnce([oldEmail])
            .mockResolvedValueOnce([oldEmail, newEmail])
            .mockResolvedValueOnce([oldEmail, newEmail]); // Stability check

        const result = await gmailUtil.waitForLatestEmailBySubject('Test Subject', true, 10000, 1000);
        
        expect(result).toEqual(newEmail);
        expect(result.date).toBe('2024-01-01T11:00:00Z');
    });

    it('should only return emails received after baseline time', async () => {
        const veryOldEmail = createMockEmail('2024-01-01T09:00:00Z', '<EMAIL>', 'Test Subject');
        const recentEmail = createMockEmail(new Date().toISOString(), '<EMAIL>', 'Test Subject');
        
        // Mock should be called with 'after' filter
        mockBrowser.checkInbox.mockResolvedValue([recentEmail]);

        const result = await gmailUtil.waitForLatestEmailBySubject('Test Subject', true, 10000, 1000);
        
        expect(result).toEqual(recentEmail);
        
        // Verify that checkInbox was called with 'after' parameter
        expect(mockBrowser.checkInbox).toHaveBeenCalledWith(
            expect.objectContaining({
                subject: 'Test Subject',
                includeBody: true,
                after: expect.any(Date)
            })
        );
    });

    it('should wait for stability before returning email', async () => {
        const email1 = createMockEmail('2024-01-01T10:00:00Z', '<EMAIL>', 'Test Subject');
        const email2 = createMockEmail('2024-01-01T11:00:00Z', '<EMAIL>', 'Test Subject');
        
        // Simulate emails arriving at different times
        mockBrowser.checkInbox
            .mockResolvedValueOnce([email1])
            .mockResolvedValueOnce([email1, email2])
            .mockResolvedValueOnce([email1, email2]); // Stability check - same latest email

        const result = await gmailUtil.waitForLatestEmailBySubject('Test Subject', true, 10000, 1000);
        
        expect(result).toEqual(email2);
        expect(mockBrowser.checkInbox).toHaveBeenCalledTimes(3);
    });

    it('should throw error when no emails found within timeout', async () => {
        mockBrowser.checkInbox.mockResolvedValue([]);

        await expect(
            gmailUtil.waitForLatestEmailBySubject('Nonexistent Subject', true, 2000, 500)
        ).rejects.toThrow('No email found with subject containing "Nonexistent Subject"');
    });
});

describe('GmailUtil - waitForNewEmailBySubjectAfterTime', () => {
    let gmailUtil: GmailUtil;
    
    beforeEach(() => {
        gmailUtil = new GmailUtil();
        jest.clearAllMocks();
    });

    const createMockEmail = (date: string, from: string, subject: string): EmailObject => ({
        from,
        subject,
        date,
        body: {
            html: '<p>Test email content</p>',
            text: 'Test email content'
        }
    });

    it('should return latest email after specified timestamp', async () => {
        const afterTime = new Date('2024-01-01T10:00:00Z');
        const email = createMockEmail('2024-01-01T11:00:00Z', '<EMAIL>', 'Test Subject');
        
        mockBrowser.checkInbox.mockResolvedValue([email]);

        const result = await gmailUtil.waitForNewEmailBySubjectAfterTime('Test Subject', afterTime, true, 5000, 1000);
        
        expect(result).toEqual(email);
        expect(mockBrowser.checkInbox).toHaveBeenCalledWith(
            expect.objectContaining({
                subject: 'Test Subject',
                includeBody: true,
                after: afterTime
            })
        );
    });

    it('should return most recent email when multiple emails exist after timestamp', async () => {
        const afterTime = new Date('2024-01-01T10:00:00Z');
        const email1 = createMockEmail('2024-01-01T11:00:00Z', '<EMAIL>', 'Test Subject');
        const email2 = createMockEmail('2024-01-01T12:00:00Z', '<EMAIL>', 'Test Subject');
        
        mockBrowser.checkInbox.mockResolvedValue([email1, email2]);

        const result = await gmailUtil.waitForNewEmailBySubjectAfterTime('Test Subject', afterTime, true, 5000, 1000);
        
        expect(result).toEqual(email2);
        expect(result.date).toBe('2024-01-01T12:00:00Z');
    });
});
