# Feature: Gmail Service Testing
#   As a QA Engineer
#   I want to test email functionality using Gmail service
#   So that I can verify email-related features in the application

#   Background:
#     Given the Gmail service is configured and authenticated

#   @GmailServiceBasic
#   Scenario Outline: Verify basic Gmail service functionality
#     When I check the Gmail inbox for emails from "<FromEmail>"
#     Then I should receive a list of emails
#     And the emails should contain valid email properties

#     @GmailServiceBasic_Test
#     Examples:
#       | FromEmail           |
#       | <EMAIL> |

#   @GmailServiceSearch
#   Scenario Outline: Search for specific emails by subject
#     When I search for emails with subject containing "<SubjectText>"
#     Then I should find emails matching the subject criteria
#     And each email should have the subject containing "<SubjectText>"

#     @GmailServiceSearch_Test
#     Examples:
#       | SubjectText           |
#       | Sensa Forgot Password |

#   @GmailServiceLatest
#   Scenario Outline: Get latest email from specific sender
#     When I get the latest email from "<SenderEmail>"
#     Then I should receive the most recent email from that sender
#     And the email should have a valid date and content

#     @GmailServiceLatest_Test
#     Examples:
#       | SenderEmail                                  |
#       | <EMAIL> |

#   @GmailServiceContent
#   Scenario Outline: Verify email content and extract links
#     Given I have an email from "<SenderEmail>" with subject containing "<SubjectText>"
#     When I verify the email content contains "<ExpectedText>"
#     Then the content verification should be successful
#     And I should be able to extract links from the email

#     @GmailServiceContent_Test
#     Examples:
#       | SenderEmail                        | SubjectText | ExpectedText |
#       | <EMAIL> | Welcome     | Thank you    |

#   @GmailServiceWait
#   Scenario Outline: Wait for email to arrive
#     When I trigger an action that sends an email to "<RecipientEmail>"
#     And I wait for an email from "<SenderEmail>" with subject "<SubjectText>"
#     Then the email should arrive within the specified timeout
#     And the email content should be accessible

#     @GmailServiceWait_Test
#     Examples:
#       | RecipientEmail                   | SenderEmail         | SubjectText |
#       | <EMAIL> | <EMAIL> | Test Email  |

  @GmailServiceLatestImproved
  Scenario Outline: Get latest email with improved reliability
    When I get the latest email with subject containing "<SubjectText>"
    Then I should receive the most recent email from that sender
    And the email should have a valid date and content

    @GmailServiceLatestImproved_Test
    Examples:
      | SubjectText           |
      | Sensa Forgot Password |

  @GmailServiceHighVolume
  Scenario Outline: Get latest email in high-volume scenarios with custom buffer
    When I wait for the latest email with subject containing "<SubjectText>" with <BufferSeconds> second buffer
    Then I should receive the most recent email from that sender
    And the email should have a valid date and content

    @GmailServiceHighVolume_Test
    Examples:
      | SubjectText           | BufferSeconds |
      | Sensa Forgot Password | 10            |

  @GmailServiceTimestamp
  Scenario Outline: Get email received after specific timestamp
    When I record the current timestamp and wait for email with subject containing "<SubjectText>"
    Then I should receive the most recent email from that sender
    And the email should have a valid date and content

    @GmailServiceTimestamp_Test
    Examples:
      | SubjectText           |
      | Sensa Forgot Password |

#   @GmailServiceCount
#   Scenario Outline: Count emails matching specific criteria
#     When I count emails from "<SenderEmail>" received after "<AfterDate>"
#     Then I should get a valid count of matching emails
#     And the count should be a non-negative number

#     @GmailServiceCount_Test
#     Examples:
#       | SenderEmail         | AfterDate  |
#       | <EMAIL> | 2024-01-01 |

#   @GmailServiceAttachments
#   Scenario Outline: Verify email with attachments
#     When I check for emails from "<SenderEmail>" with attachments
#     Then I should find emails that include attachments
#     And the attachment data should be accessible

#     @GmailServiceAttachments_Test
#     Examples:
#       | SenderEmail         |
#       | <EMAIL> |

#   @GmailServiceLabels
#   Scenario Outline: Check emails in different labels
#     When I check emails in the "<Label>" label
#     Then I should receive emails from the specified label
#     And the emails should be properly categorized

#     @GmailServiceLabels_Test
#     Examples:
#       | Label |
#       | INBOX |
#       | SPAM  |

#   @GmailServiceDateFilter
#   Scenario Outline: Filter emails by date range
#     When I search for emails received between "<StartDate>" and "<EndDate>"
#     Then I should find emails within the specified date range
#     And all returned emails should have dates within the range

#     @GmailServiceDateFilter_Test
#     Examples:
#       | StartDate  | EndDate    |
#       | 2024-01-01 | 2024-12-31 |
