# Gmail Utility Improvements - Latest Email Detection

## Problem Solved

The original `waitForLatestEmailBySubject` function had several issues that could cause it to return old emails instead of the truly latest one:

1. **No time-based filtering**: Searched through ALL emails with matching subject, including old ones
2. **Race condition**: 2-second delay could miss emails arriving during that period
3. **No baseline timestamp**: Could return emails that existed before the function was called

## Solution Implemented

### Enhanced `waitForLatestEmailBySubject` Method

**Key Improvements:**
- **Time-based filtering**: Uses `after` parameter to only look for emails received after function starts
- **Baseline timestamp**: Establishes a baseline with configurable buffer time for email delivery delays
- **Stability checking**: Waits for consecutive checks with same latest email before returning
- **Better logging**: Detailed logs for debugging email detection issues

**New Parameters:**
```typescript
public async waitForLatestEmailBySubject(
    subjectText: string,
    includeBody: boolean = true,
    timeoutMs: number = 60000,
    intervalMs: number = 3000,
    bufferTimeMs: number = 5000,  // NEW: Buffer for email delivery delays
): Promise<EmailObject>
```

**How it works:**
1. Sets baseline timestamp (current time - buffer)
2. Only searches for emails received after baseline
3. Tracks latest email found
4. Waits for stability (same latest email for 2 consecutive checks)
5. Returns the truly latest email

### New `waitForNewEmailBySubjectAfterTime` Method

For scenarios where you need precise control over the timestamp:

```typescript
public async waitForNewEmailBySubjectAfterTime(
    subjectText: string,
    afterTimestamp: Date,
    includeBody: boolean = true,
    timeoutMs: number = 60000,
    intervalMs: number = 3000,
): Promise<EmailObject>
```

**Use cases:**
- When you trigger an action and want emails received only after that action
- When you have a specific timestamp reference point
- For more predictable testing scenarios

## Usage Examples

### Basic Usage (Recommended)
```typescript
const gmailUtil = new GmailUtil();

// Wait for latest email with improved reliability
const latestEmail = await gmailUtil.waitForLatestEmailBySubject(
    'Password Reset',
    true,     // include body
    60000,    // 60 second timeout
    3000,     // check every 3 seconds
    5000      // 5 second buffer for email delivery
);
```

### Precise Timestamp Control
```typescript
const gmailUtil = new GmailUtil();

// Record timestamp before triggering action
const actionTime = new Date();

// Trigger your action that sends email
await triggerPasswordReset();

// Wait for email received after the action
const newEmail = await gmailUtil.waitForNewEmailBySubjectAfterTime(
    'Password Reset',
    actionTime,
    true,
    30000,
    2000
);
```

### High-Volume Email Scenarios
```typescript
const gmailUtil = new GmailUtil();

// For scenarios with many emails being triggered
const latestEmail = await gmailUtil.waitForLatestEmailBySubject(
    'Order Confirmation',
    true,
    90000,    // Longer timeout for busy systems
    2000,     // More frequent checks
    10000     // Larger buffer for delivery delays
);
```

## Benefits

1. **Reliability**: Always gets emails received after function call
2. **Performance**: Filters emails at API level, reducing data transfer
3. **Stability**: Waits for email flow to stabilize before returning
4. **Flexibility**: Two methods for different use cases
5. **Debugging**: Enhanced logging for troubleshooting

## Migration Guide

### Before (Old Method)
```typescript
// Could return old emails
const email = await gmailUtil.waitForLatestEmailBySubject('Subject');
```

### After (New Method)
```typescript
// Guaranteed to return latest email after function call
const email = await gmailUtil.waitForLatestEmailBySubject('Subject');

// Or with custom buffer time
const email = await gmailUtil.waitForLatestEmailBySubject(
    'Subject', 
    true, 
    60000, 
    3000, 
    2000  // 2 second buffer instead of default 5 seconds
);
```

## Testing

Run the test suite to verify functionality:
```bash
npm test tests/support/utils/gmail.util.test.ts
```

The tests cover:
- Multiple emails with different timestamps
- Time-based filtering verification
- Stability checking behavior
- Error handling for timeout scenarios
